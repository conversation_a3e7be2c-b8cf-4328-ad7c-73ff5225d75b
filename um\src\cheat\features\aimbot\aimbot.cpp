#include "pch.h"
#include "../../entity.hpp"
#include "../../globals.hpp"
#include "../../gamedata.hpp"

// ===========================
// AIMBOT IMPLEMENTATION
// ===========================

void Aimbot::startAimbotThread() {
  if (!isRunning.load()) {
    shouldStop.store(false);
    isRunning.store(true);
    aimbotThread = std::thread(&Aimbot::aimbotThreadFunction, this);
    aimbotThread.detach(); // Detach for better performance
  }
}

void Aimbot::stopAimbotThread() {
  shouldStop.store(true);
  isRunning.store(false);
}

void Aimbot::aimbotThreadFunction() {
  lastUpdate = std::chrono::steady_clock::now();

  while (!shouldStop.load()) {
    auto now = std::chrono::steady_clock::now();

    // High precision timing
    if (now - lastUpdate >= updateInterval) {
      std::lock_guard<std::mutex> lock(aimbotMutex);

      // Only run aimbot if enabled and GameData is ready
      if (globals::Legitbot::enabled && GameData::isInitialized()) {
        doAimbot();
      }

      lastUpdate = now;
    }

    // Small sleep to prevent 100% CPU usage
    std::this_thread::sleep_for(std::chrono::microseconds(100));
  }

  isRunning.store(false);
}

void Aimbot::doAimbot() {
  std::vector<Vector> playerPositions;
  playerPositions.clear();

  // Get game data using GameData API
  auto viewMatrix = GameData::getViewMatrix();
  auto localTeam = GameData::getLocalTeam();

  // Use the existing reader system from the global reader instance
  auto playerList = reader.getPlayerListCopy();

  // Debug output
  static int debugCounter = 0;
  debugCounter++;
  if (debugCounter % 500 == 0) {
    std::cout << "[Aimbot Debug] Players: " << playerList.size() << ", LocalTeam: " << localTeam << std::endl;
  }

  for (const auto& player : playerList) {
    // Get bone position using GameData API
    Vector playerPosition = GameData::getBonePosition(player.BoneArray, bones::head);

    if (player.health <= 0 || player.health > 100)
      continue;

    // Temporarily disable all checks for testing
    // if (!player.PlayerSpotted && player.team != localTeam && globals::Legitbot::visiblecheck)
    //   continue;

    // if (player.team == localTeam && globals::Legitbot::teamcheck)
    //   continue;

    Vector h;
    if (Vector::world_to_screen(viewMatrix, playerPosition, h)) {
      playerPositions.push_back(h);
    }
  }

  if (debugCounter % 500 == 0 && !playerPositions.empty()) {
    std::cout << "[Aimbot Debug] Valid targets: " << playerPositions.size() << std::endl;
  }

  if (globals::Legitbot::Circle::enabled) {
    // Render circle using globals for screen center
    float centerX = static_cast<float>(globals::Screen::width) / 2.0f;
    float centerY = static_cast<float>(globals::Screen::height) / 2.0f;
    // Note: Circle rendering should be handled in visuals thread for thread safety
  }

  if (GetAsyncKeyState(0x58) & 0x8000) { // X on keyboard - proper key check
    if (debugCounter % 100 == 0) {
      std::cout << "[Aimbot Debug] X pressed, targets: " << playerPositions.size() << std::endl;
    }

    auto closest_player = findClosest(playerPositions);
    if (!closest_player.IsZero()) {
      if (debugCounter % 100 == 0) {
        std::cout << "[Aimbot Debug] Moving to: " << closest_player.x << ", " << closest_player.y << std::endl;
      }
      MoveMouseToPlayer(closest_player);
    }
  }
}

Vector Aimbot::findClosest(const std::vector<Vector>& playerPositions) {
  if (playerPositions.empty()) return Vector{0,0,0};

  Vector center_of_screen{
    static_cast<float>(globals::Screen::width) / 2.0f,
    static_cast<float>(globals::Screen::height) / 2.0f,
    0.0f
  };

  float max_distance_sq = 5 * globals::Legitbot::radius * globals::Legitbot::radius * 5;
  float closest_distance_sq = FLT_MAX;
  Vector closest = Vector{0,0,0};

  for (const auto& pos : playerPositions) {
    float dx = pos.x - center_of_screen.x;
    float dy = pos.y - center_of_screen.y;
    float distance_sq = dx*dx + dy*dy;

    if (distance_sq < closest_distance_sq && distance_sq < max_distance_sq) {
      closest_distance_sq = distance_sq;
      closest = pos;
    }
  }
  return closest;
}

void Aimbot::MoveMouseToPlayer(Vector position) {
  if (position.IsZero())
    return;

  POINT currentMousePos;
  GetCursorPos(&currentMousePos);
  Vector currentPos{
    static_cast<float>(currentMousePos.x),
    static_cast<float>(currentMousePos.y),
    0.0f
  };

  float deltaX = position.x - currentPos.x;
  float deltaY = position.y - currentPos.y;

  static int debugCounter = 0;
  debugCounter++;

  if (debugCounter % 50 == 0) {
    std::cout << "[Aimbot Debug] Mouse: (" << currentPos.x << ", " << currentPos.y << ") -> ("
              << position.x << ", " << position.y << "), Delta: (" << deltaX << ", " << deltaY << ")" << std::endl;
  }

  // Test with direct movement first (no smoothing)
  if (globals::Legitbot::smoothness <= 1.0f) {
    if (debugCounter % 50 == 0) {
      std::cout << "[Aimbot Debug] Direct move: " << deltaX << ", " << deltaY << std::endl;
    }
    mouse_event(MOUSEEVENTF_MOVE, static_cast<LONG>(std::round(deltaX)),
      static_cast<LONG>(std::round(deltaY)), 0, 0);
    return;
  }

  // Smoothed movement
  const float base_smoothness = (globals::Legitbot::smoothness > 1.0f) ? globals::Legitbot::smoothness : 1.0f;
  const float distance = std::sqrt(deltaX * deltaX + deltaY * deltaY);
  const float adaptive_smoothness = base_smoothness * (1.0f + distance / 1000.0f);

  float stepX = deltaX / adaptive_smoothness;
  float stepY = deltaY / adaptive_smoothness;

  accumulatedX += stepX;
  accumulatedY += stepY;

  LONG moveX = static_cast<LONG>(std::round(accumulatedX));
  LONG moveY = static_cast<LONG>(std::round(accumulatedY));

  accumulatedX -= moveX;
  accumulatedY -= moveY;

  const float deadzone = std::clamp(0.2f * adaptive_smoothness, 0.1f, 2.0f);
  if (std::abs(deltaX) < deadzone && std::abs(deltaY) < deadzone) {
    accumulatedX += deltaX;
    accumulatedY += deltaY;
    return;
  }

  if (debugCounter % 50 == 0) {
    std::cout << "[Aimbot Debug] Smooth move: " << moveX << ", " << moveY << std::endl;
  }

  mouse_event(MOUSEEVENTF_MOVE, moveX, moveY, 0, 0);
}

void Aimbot::renderAimbotVisuals() {
  // Rendering is now handled in visuals.cpp for thread safety
  // This function is kept for API compatibility but does nothing
}