#include "pch.h"
#include "../../entity.hpp"
#include "../../globals.hpp"
#include "../../gamedata.hpp"

// ===========================
// AIMBOT IMPLEMENTATION
// ===========================

void Aimbot::startAimbotThread() {
  if (!isRunning.load()) {
    shouldStop.store(false);
    isRunning.store(true);
    aimbotThread = std::thread(&Aimbot::aimbotThreadFunction, this);
    aimbotThread.detach(); // Detach for better performance
  }
}

void Aimbot::stopAimbotThread() {
  shouldStop.store(true);
  isRunning.store(false);
}

void Aimbot::aimbotThreadFunction() {
  lastUpdate = std::chrono::steady_clock::now();

  while (!shouldStop.load()) {
    auto now = std::chrono::steady_clock::now();

    // High precision timing
    if (now - lastUpdate >= updateInterval) {
      std::lock_guard<std::mutex> lock(aimbotMutex);

      // Only run aimbot if enabled and GameData is ready
      if (globals::Legitbot::enabled && GameData::isInitialized()) {
        doAimbot();
      }

      lastUpdate = now;
    }

    // Small sleep to prevent 100% CPU usage
    std::this_thread::sleep_for(std::chrono::microseconds(100));
  }

  isRunning.store(false);
}

void Aimbot::doAimbot() {
  // Check if GameData system is ready
  if (!GameData::isInitialized()) {
    return;
  }

  std::vector<Vector> playerPositions;
  playerPositions.clear();

  // Get game data using GameData API
  auto viewMatrix = GameData::getViewMatrix();
  auto localTeam = GameData::getLocalTeam();

  // Use the existing reader system from the global reader instance
  auto playerList = reader.getPlayerListCopy();

  // Debug: Check if we have players
  static int debugCounter = 0;
  debugCounter++;
  if (debugCounter % 1000 == 0) { // Print every 1000 calls (about once per second)
    std::cout << "[Aimbot Debug] Players found: " << playerList.size() << ", LocalTeam: " << localTeam << std::endl;

    // Print all players for debugging
    for (size_t i = 0; i < playerList.size(); ++i) {
      const auto& player = playerList[i];
      std::cout << "[Aimbot Debug] Player " << i << ": " << player.PlayerName
                << ", Team: " << player.team << ", Health: " << player.health
                << ", BoneArray: 0x" << std::hex << player.BoneArray << std::dec << std::endl;
    }
  }

  int processedPlayers = 0;
  int validTargets = 0;

  for (const auto& player : playerList) {
    processedPlayers++;

    // Debug: Print every player being processed
    if (debugCounter % 1000 == 0) {
      std::cout << "[Aimbot Debug] Processing Player " << processedPlayers << ": " << player.PlayerName
                << ", Health: " << player.health << ", BoneArray: 0x" << std::hex << player.BoneArray << std::dec << std::endl;
    }

    // Use GameData API to get bone position
    Vector playerPosition = GameData::getBonePosition(player.BoneArray, bones::head);

    if (debugCounter % 1000 == 0) {
      std::cout << "[Aimbot Debug] Bone position: (" << playerPosition.x << ", " << playerPosition.y << ", " << playerPosition.z << ")" << std::endl;
    }

    if (player.health <= 0 || player.health > 100) {
      if (debugCounter % 1000 == 0) {
        std::cout << "[Aimbot Debug] Player filtered by health: " << player.health << std::endl;
      }
      continue;
    }

    // TEMPORARILY DISABLE ALL CHECKS - TARGET ALL VALID PLAYERS
    // Team check disabled for testing
    // if (globals::Legitbot::teamcheck && player.team == localTeam) {
    //   continue;
    // }

    // Visibility check disabled for testing
    // if (globals::Legitbot::visiblecheck && !player.PlayerSpotted && player.team != localTeam) {
    //   continue;
    // }

    Vector h;
    if (Vector::world_to_screen(viewMatrix, playerPosition, h)) {
      playerPositions.push_back(h);
      validTargets++;

      if (debugCounter % 1000 == 0) {
        std::cout << "[Aimbot Debug] Valid target added: (" << h.x << ", " << h.y << ")" << std::endl;
      }
    } else {
      if (debugCounter % 1000 == 0) {
        std::cout << "[Aimbot Debug] Player failed world_to_screen" << std::endl;
      }
    }
  }

  if (debugCounter % 1000 == 0) {
    std::cout << "[Aimbot Debug] Processed " << processedPlayers << " players, found " << validTargets << " valid targets" << std::endl;
  }

  // Debug: Check if we have valid targets
  if (debugCounter % 1000 == 0 && !playerPositions.empty()) {
    std::cout << "[Aimbot Debug] Valid targets: " << playerPositions.size() << std::endl;
  }

  // Check for aimbot key (X key) - use & 0x8000 for proper key state check
  if (GetAsyncKeyState(0x58) & 0x8000) { // X on keyboard
    if (debugCounter % 100 == 0) { // More frequent debug for key press
      std::cout << "[Aimbot Debug] X key pressed, targets: " << playerPositions.size() << std::endl;
    }

    auto closest_player = findClosest(playerPositions);
    if (!closest_player.IsZero()) {
      if (debugCounter % 100 == 0) {
        std::cout << "[Aimbot Debug] Moving to target: " << closest_player.x << ", " << closest_player.y << std::endl;
      }
      MoveMouseToPlayer(closest_player);
    }
  }
}

Vector Aimbot::findClosest(const std::vector<Vector>& playerPositions) {
  if (playerPositions.empty()) return Vector{0, 0, 0};

  Vector center_of_screen{
    static_cast<float>(globals::Screen::width) / 2.0f,
    static_cast<float>(globals::Screen::height) / 2.0f,
    0.0f
  };

  // Make radius much larger for testing - use a large default if radius is too small
  float effectiveRadius = (globals::Legitbot::radius < 10.0f) ? 500.0f : globals::Legitbot::radius;
  float max_distance_sq = effectiveRadius * effectiveRadius;
  float closest_distance_sq = FLT_MAX;
  Vector closest = Vector{0, 0, 0};

  static int debugCounter = 0;
  debugCounter++;

  for (const auto& pos : playerPositions) {
    float dx = pos.x - center_of_screen.x;
    float dy = pos.y - center_of_screen.y;
    float distance_sq = dx * dx + dy * dy;
    float distance = std::sqrt(distance_sq);

    if (debugCounter % 100 == 0) {
      std::cout << "[Aimbot Debug] Target at (" << pos.x << ", " << pos.y << "), distance: " << distance << ", max: " << effectiveRadius << std::endl;
    }

    if (distance_sq < closest_distance_sq && distance_sq < max_distance_sq) {
      closest_distance_sq = distance_sq;
      closest = pos;
    }
  }

  if (debugCounter % 100 == 0 && !closest.IsZero()) {
    std::cout << "[Aimbot Debug] Closest target selected: (" << closest.x << ", " << closest.y << ")" << std::endl;
  }

  return closest;
}

void Aimbot::MoveMouseToPlayer(Vector position) {
  if (position.IsZero())
    return;

  POINT currentMousePos;
  GetCursorPos(&currentMousePos);
  Vector currentPos{
    static_cast<float>(currentMousePos.x),
    static_cast<float>(currentMousePos.y),
    0.0f
  };

  float deltaX = position.x - currentPos.x;
  float deltaY = position.y - currentPos.y;

  static int debugCounter = 0;
  debugCounter++;

  if (debugCounter % 50 == 0) {
    std::cout << "[Aimbot Debug] Mouse move: from (" << currentPos.x << ", " << currentPos.y << ") to (" << position.x << ", " << position.y << ")" << std::endl;
    std::cout << "[Aimbot Debug] Delta: (" << deltaX << ", " << deltaY << "), Smoothness: " << globals::Legitbot::smoothness << std::endl;
  }

  // For testing, use direct movement first (smoothness = 1)
  if (globals::Legitbot::smoothness <= 1.0f) {
    if (debugCounter % 50 == 0) {
      std::cout << "[Aimbot Debug] Direct movement: (" << deltaX << ", " << deltaY << ")" << std::endl;
    }
    mouse_event(MOUSEEVENTF_MOVE, static_cast<LONG>(std::round(deltaX)),
      static_cast<LONG>(std::round(deltaY)), 0, 0);
    return;
  }

  const float base_smoothness = (globals::Legitbot::smoothness > 1.0f) ? globals::Legitbot::smoothness : 1.0f;
  const float distance = std::sqrt(deltaX * deltaX + deltaY * deltaY);
  const float adaptive_smoothness = base_smoothness * (1.0f + distance / 1000.0f);

  float stepX = deltaX / adaptive_smoothness;
  float stepY = deltaY / adaptive_smoothness;

  accumulatedX += stepX;
  accumulatedY += stepY;

  LONG moveX = static_cast<LONG>(std::round(accumulatedX));
  LONG moveY = static_cast<LONG>(std::round(accumulatedY));

  accumulatedX -= moveX;
  accumulatedY -= moveY;

  const float deadzone_calc = 0.2f * adaptive_smoothness;
  const float deadzone = (deadzone_calc < 0.1f) ? 0.1f : (deadzone_calc > 2.0f) ? 2.0f : deadzone_calc;
  if (std::abs(deltaX) < deadzone && std::abs(deltaY) < deadzone) {
    accumulatedX += deltaX;
    accumulatedY += deltaY;
    return;
  }

  if (debugCounter % 50 == 0) {
    std::cout << "[Aimbot Debug] Smooth movement: (" << moveX << ", " << moveY << ")" << std::endl;
  }

  mouse_event(MOUSEEVENTF_MOVE, moveX, moveY, 0, 0);
}

void Aimbot::renderAimbotVisuals() {
  // Rendering is now handled in visuals.cpp for thread safety
  // This function is kept for API compatibility but does nothing
}